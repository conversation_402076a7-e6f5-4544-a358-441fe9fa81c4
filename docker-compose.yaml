name: 'mcp-servers'
services:
  k8s-mcp-server:
    image: ginnux/k8s-mcp-server:latest # Or a specific version
    container_name: k8s-mcp-server
    ports:
      - "32423:32423" # Host:Container, adjust if using a different SERVER_PORT
    volumes:
      - ~/.kube:/home/<USER>/.kube:ro # Mount kubeconfig read-only to non-root user home
    environment:
      - KUBECONFIG=/home/<USER>/.kube/config
      - SERVER_MODE=sse # Can be 'stdio', 'sse', or 'streamable-http'
      - SERVER_PORT=32423 # Port for SSE/streamable-http modes
    command: ["--read-only"] # Uncomment this line to enable read-only mode
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:32423/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # To run in stdio mode with docker-compose, you might need to adjust 'ports',
    # add 'stdin_open: true' and 'tty: true', and potentially override the command.
    # For example, to force stdio mode:
    # command: ["--mode", "stdio"]
    # stdin_open: true
    # tty: true
    # For streamable-http mode, simply change SERVER_MODE to 'streamable-http'