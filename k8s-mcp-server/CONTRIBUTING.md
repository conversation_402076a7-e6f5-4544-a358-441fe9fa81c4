# Contributing

Thank you for your interest in contributing to this project!

## How to Contribute

1.  **Fork the repository**
2.  **Create a new branch:** `git checkout -b feature/your-feature-name`
3.  **Make your changes**
4.  **Commit your changes:** `git commit -m 'Add some feature'`
5.  **Push to the branch:** `git push origin feature/your-feature-name`
6.  **Open a Pull Request**

## Reporting Bugs

Please open an issue on GitHub and provide as much detail as possible, including steps to reproduce the bug.

## Feature Requests

Feel free to open an issue on GitHub to suggest new features or improvements.

## Code Style

Please follow the existing code style and ensure your code passes any linting checks.

## License

By contributing, you agree that your contributions will be licensed under the [MIT License](LICENSE).
