name: B<PERSON> and Push Docker Image and Binaries

on:
  push:
    tags:
      - 'v*'

jobs:
  build-binaries:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm64
          - os: darwin
            arch: amd64
          - os: darwin
            arch: arm64
          - os: windows
            arch: amd64
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.23'
          check-latest: true

      - name: Extract tag name
        id: get_tag
        run: echo "TAG_NAME=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Verify Go installation
        run: |
          go version
          go env GOVERSION

      - name: Build binary
        env:
          GOOS: ${{ matrix.os }}
          GOARCH: ${{ matrix.arch }}
          CGO_ENABLED: 0
          GOTOOLCHAIN: auto
        run: |
          binary_name="k8s-mcp-server"
          if [ "${{ matrix.os }}" = "windows" ]; then
            binary_name="${binary_name}.exe"
          fi
          
          echo "Building for ${{ matrix.os }}/${{ matrix.arch }}"
          go build -ldflags="-w -s" -o ${binary_name} main.go
          
          # Verify binary was created
          ls -la ${binary_name}
          
          # Create archive
          archive_name="k8s-mcp-server-${{ steps.get_tag.outputs.TAG_NAME }}-${{ matrix.os }}-${{ matrix.arch }}"
          if [ "${{ matrix.os }}" = "windows" ]; then
            zip ${archive_name}.zip ${binary_name}
            echo "Created ${archive_name}.zip"
          else
            tar -czf ${archive_name}.tar.gz ${binary_name}
            echo "Created ${archive_name}.tar.gz"
          fi

      - name: Upload binaries as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: binary-${{ matrix.os }}-${{ matrix.arch }}
          path: k8s-mcp-server-*
          retention-days: 30

  build-and-push-docker:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract tag name
        id: get_tag
        run: echo "TAG_NAME=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push multi-platform Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ginnux/k8s-mcp-server:${{ steps.get_tag.outputs.TAG_NAME }}
            ginnux/k8s-mcp-server:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  create-release:
    needs: [build-binaries, build-and-push-docker]
    runs-on: ubuntu-latest
    
    steps:
      - name: Extract tag name
        id: get_tag
        run: echo "TAG_NAME=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: ./artifacts
          merge-multiple: true

      - name: List downloaded files
        run: |
          echo "Downloaded files:"
          find ./artifacts -type f -name "k8s-mcp-server-*" | sort

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: ./artifacts/k8s-mcp-server-*
          tag_name: v${{ steps.get_tag.outputs.TAG_NAME }}
          name: Release v${{ steps.get_tag.outputs.TAG_NAME }}
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}